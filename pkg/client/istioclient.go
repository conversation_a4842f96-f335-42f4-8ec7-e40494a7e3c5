package client

import (
	"context"
	"istio.io/client-go/pkg/apis/networking/v1beta1"
	istioclientset "istio.io/client-go/pkg/clientset/versioned"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/rest"
	"k8s.io/klog/v2"
)

// IstioClientInterface
type IstioClientInterface interface {
	GetVirtualService(ctx context.Context, ns, name string, opts metav1.GetOptions) (*v1beta1.VirtualService, error)
	UpdateVirtualService(ctx context.Context, ns string, vs *v1beta1.VirtualService, opts metav1.UpdateOptions) error
	CreateVirtualService(ctx context.Context, ns string, vs *v1beta1.VirtualService, opts metav1.CreateOptions) error
	DeleteVirtualService(ctx context.Context, ns, name string, opts metav1.DeleteOptions) error
}

type IstioClient struct {
	Clientset istioclientset.Interface
}

var _ IstioClientInterface = &IstioClient{}

func NewIstioClient(cfg *rest.Config) (*IstioClient, error) {
	c, err := istioclientset.NewForConfig(cfg)
	if err != nil {
		return nil, err
	}
	return &IstioClient{Clientset: c}, nil
}

func (ic *IstioClient) GetVirtualService(ctx context.Context, ns, name string, opt metav1.GetOptions) (*v1beta1.VirtualService, error) {
	return ic.Clientset.NetworkingV1beta1().VirtualServices(ns).Get(ctx, name, opt)
}

func (ic *IstioClient) UpdateVirtualService(ctx context.Context, ns string, vs *v1beta1.VirtualService,
	opt metav1.UpdateOptions) error {
	_, err := ic.Clientset.NetworkingV1beta1().VirtualServices(ns).Update(ctx, vs, opt)
	if err != nil {
		klog.Errorf("Error updating VirtualService %s/%s: %v", ns, vs.Name, err)
		return err
	}
	return nil
}

func (ic *IstioClient) CreateVirtualService(ctx context.Context, ns string, vs *v1beta1.VirtualService, opt metav1.CreateOptions) error {
	_, err := ic.Clientset.NetworkingV1beta1().VirtualServices(ns).Create(ctx, vs, opt)
	if err != nil {
		klog.Errorf("Error creating VirtualService %s/%s: %v", ns, vs.Name, err)
		return err
	}
	return nil
}

func (ic *IstioClient) DeleteVirtualService(ctx context.Context, ns, name string, opts metav1.DeleteOptions) error {
	err := ic.Clientset.NetworkingV1beta1().VirtualServices(ns).Delete(ctx, name, opts)
	if err != nil {
		klog.Errorf("Error deleting VirtualService %s/%s: %v", ns, name, err)
		return err
	}
	return nil
}
