package multi

import (
	"context"
	"fmt"
	"k8s.io/client-go/tools/clientcmd"
	"kuberay-dynamic-route-manager/pkg/client"
	"kuberay-dynamic-route-manager/pkg/controller/dynamicroute"
	"strings"
	"sync"

	"k8s.io/klog/v2"
	"kuberay-dynamic-route-manager/pkg/controller"
)

// MultiClusterControllerImpl 实现了 controller.MultiClusterController 接口
type MultiClusterControllerImpl struct {
	controllers map[string]controller.Controller
	lock        sync.RWMutex
}

// 确保 MultiClusterControllerImpl 实现了 MultiClusterController 接口
var _ controller.MultiClusterController = &MultiClusterControllerImpl{}

func NewMultiClusterController(kubeconfigPaths string) (controller.MultiClusterController, error) {
	if kubeconfigPaths == "" {
		return nil, fmt.Errorf("必须指定至少一个kubeconfig路径")
	}

	m := &MultiClusterControllerImpl{
		controllers: make(map[string]controller.Controller),
	}

	// 解析逗号分隔的路径
	paths := strings.Split(kubeconfigPaths, ",")

	for _, path := range paths {
		// 加载kubeconfig
		config, err := clientcmd.LoadFromFile(path)
		if err != nil {
			return nil, fmt.Errorf("无法加载kubeconfig %s: %v", path, err)
		}

		// 获取当前上下文名称
		currentContext := config.CurrentContext
		if currentContext == "" {
			return nil, fmt.Errorf("kubeconfig %s 中没有设置当前上下文", path)
		}

		// 获取上下文信息
		cctx, exists := config.Contexts[currentContext]
		if !exists {
			return nil, fmt.Errorf("在kubeconfig %s 中找不到上下文 %s", path, currentContext)
		}

		// 使用集群名称作为控制器标识
		clusterName := cctx.Cluster

		// 构建rest.Config
		restConfig, err := clientcmd.BuildConfigFromFlags("", path)
		if err != nil {
			return nil, fmt.Errorf("无法从 %s 构建kubeconfig: %v", path, err)
		}

		// 创建k8s客户端
		k8sClient, err := client.NewK8sClient(restConfig)
		if err != nil {
			return nil, fmt.Errorf("无法为集群 %s 创建k8s客户端: %v", clusterName, err)
		}

		// 创建istio客户端
		istioClient, err := client.NewIstioClient(restConfig)
		if err != nil {
			return nil, fmt.Errorf("无法为集群 %s 创建istio客户端: %v", clusterName, err)
		}

		// 创建并注册控制器
		ctrl := dynamicroute.NewDynamicRouteController(k8sClient.Clientset, istioClient)
		m.AddController(clusterName, ctrl)
	}

	return m, nil
}

// AddController 实现 MultiClusterController 接口
func (m *MultiClusterControllerImpl) AddController(clusterName string, ctrl controller.Controller) error {
	m.lock.Lock()
	defer m.lock.Unlock()

	if _, exists := m.controllers[clusterName]; exists {
		return fmt.Errorf("集群 %s 的控制器已存在", clusterName)
	}

	m.controllers[clusterName] = ctrl
	return nil
}

// RemoveController 实现 MultiClusterController 接口
func (m *MultiClusterControllerImpl) RemoveController(clusterName string) error {
	m.lock.Lock()
	defer m.lock.Unlock()

	if ctrl, exists := m.controllers[clusterName]; exists {
		ctrl.Stop()
		delete(m.controllers, clusterName)
		return nil
	}
	return fmt.Errorf("集群 %s 的控制器不存在", clusterName)
}

// GetController 实现 MultiClusterController 接口
func (m *MultiClusterControllerImpl) GetController(clusterName string) (controller.Controller, bool) {
	m.lock.RLock()
	defer m.lock.RUnlock()

	ctrl, exists := m.controllers[clusterName]
	return ctrl, exists
}

// Run 并发启动所有集群的controller
func (m *MultiClusterControllerImpl) Run(ctx context.Context) {
	m.lock.Lock()
	defer m.lock.Unlock()

	var wg sync.WaitGroup

	for clusterName, ctrl := range m.controllers {
		wg.Add(1)
		go func(name string, c controller.Controller) {
			defer wg.Done()
			klog.Infof("Starting controller for cluster: %s", name)
			c.Run(ctx)
			klog.Infof("Controller for cluster %s stopped", name)
		}(clusterName, ctrl)
	}

	// 等待所有 controller goroutine 退出
	wg.Wait()
	klog.Info("All cluster controllers have been stopped")
}

// Stop 实现 Controller 接口
func (m *MultiClusterControllerImpl) Stop() {
	m.lock.Lock()
	defer m.lock.Unlock()

	for _, ctrl := range m.controllers {
		ctrl.Stop()
	}
}
