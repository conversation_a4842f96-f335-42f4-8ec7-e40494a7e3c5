package controller

import "context"

// Controller 定义了所有控制器必须实现的接口
type Controller interface {
	// Run 启动控制器
	Run(ctx context.Context)
	// Stop 停止控制器
	Stop()
}

// MultiClusterController 定义了多集群控制器的接口
type MultiClusterController interface {
	Controller
	// AddController 添加单集群控制器
	AddController(clusterName string, ctrl Controller) error
	// RemoveController 移除单集群控制器
	RemoveController(clusterName string) error
	// GetController 获取指定集群的控制器
	GetController(clusterName string) (Controller, bool)
}
