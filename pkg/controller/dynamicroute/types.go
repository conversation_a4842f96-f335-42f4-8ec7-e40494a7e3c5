package dynamicroute

const (
	VirtualServiceNamespace = "kuberay" // vs只在kuberay里面维护

	DefaultHosts   = "ray.ttyuyin.com"
	DefaultGateway = "istio-ingress/zero-trust"

	// 网关级别的 VS 配置
	GatewayVirtualServiceName = "kuberay-gateway-vs"

	// 服务级别的 VS 命名格式
	ServiceVSNameFormat = "%s-routes"

	ServiceFqdnFormat = "%s.%s.svc.cluster.local"

	// 注解相关常量
	ManagedByAnnotationKey   = "managed-by"
	ManagedByAnnotationValue = "kuberay-dynamic-route"

	RayClusterLabelKey = "ray.io/cluster"

	OnlineDevelopmentTypeAnnotationKey = "online-dev-type"

	CodeServer = "code-server"
	Jupyter    = "jupyter"
)

type Operation string

const (
	OperationAdd    Operation = "ADD"
	OperationDelete Operation = "DELETE"
)

// WorkQueueItem 表示队列中的一个项目
type WorkQueueItem struct {
	Key       string    // 资源的 namespace/name
	Operation Operation // 操作类型
}

// ServiceInfo 包含服务的基本信息
type ServiceInfo struct {
	Name           string
	Namespace      string
	DevType        string
	RayClusterName string
	Ports          []ServicePort
}

// ServicePort 表示服务端口信息
type ServicePort struct {
	Name string
	Port int32
}
