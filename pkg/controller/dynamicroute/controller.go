package dynamicroute

import (
	"context"
	"fmt"
	meta "k8s.io/apimachinery/pkg/api/meta"
	"sync"
	"time"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/util/workqueue"
	"kuberay-dynamic-route-manager/pkg/client"
	"kuberay-dynamic-route-manager/pkg/controller"

	"k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/informers"
	corev1 "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"
)

// DynamicRouteController 动态路由控制器
type DynamicRouteController struct {
	k8sClient       kubernetes.Interface
	istioClient     client.IstioClientInterface
	svcLister       corev1.ServiceLister
	svcSynced       cache.InformerSynced
	workqueue       workqueue.RateLimitingInterface
	informerFactory informers.SharedInformerFactory
	stopCh          chan struct{}
	lock            sync.RWMutex

	// 业务处理组件
	serviceHandler *ServiceHandler
}

var _ controller.Controller = &DynamicRouteController{}

// NewDynamicRouteController 创建新的动态路由控制器
func NewDynamicRouteController(k8sClient kubernetes.Interface, istioClient client.IstioClientInterface) *DynamicRouteController {
	factory := informers.NewSharedInformerFactory(k8sClient, time.Minute)
	svcInformer := factory.Core().V1().Services()

	// 创建业务处理组件
	vsManager := NewVirtualServiceManager(istioClient)
	serviceHandler := NewServiceHandler(svcInformer.Lister(), vsManager)

	c := &DynamicRouteController{
		k8sClient:       k8sClient,
		istioClient:     istioClient,
		svcLister:       svcInformer.Lister(),
		svcSynced:       svcInformer.Informer().HasSynced,
		workqueue:       workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), "Services"),
		informerFactory: factory,
		stopCh:          make(chan struct{}),
		serviceHandler:  serviceHandler,
	}

	// 注册事件处理回调
	svcInformer.Informer().AddEventHandler(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				c.enqueueWithOperation(obj, OperationAdd)
			},
			DeleteFunc: func(obj interface{}) {
				c.enqueueWithOperation(obj, OperationDelete)
			},
		},
	)

	return c
}

// enqueueWithOperation 将事件对象和操作类型入队
func (c *DynamicRouteController) enqueueWithOperation(obj interface{}, op Operation) {
	// 获取元数据
	metaObj, err := meta.Accessor(obj)
	if err != nil {
		runtime.HandleError(fmt.Errorf("获取对象元数据失败: %v", err))
		return
	}

	// 检查是否有指定标签
	if value, exists := metaObj.GetAnnotations()[ManagedByAnnotationKey]; !exists || value != ManagedByAnnotationValue {
		// 没有指定标签，忽略此服务
		klog.V(4).Infof("忽略未标记的服务: %s/%s", metaObj.GetNamespace(), metaObj.GetName())
		return
	}

	key, err := cache.MetaNamespaceKeyFunc(obj)
	if err != nil {
		runtime.HandleError(fmt.Errorf("error creating key for object: %v", err))
		return
	}

	item := WorkQueueItem{
		Key:       key,
		Operation: op,
	}
	c.workqueue.Add(item)
}

// Run 启动控制器
func (c *DynamicRouteController) Run(ctx context.Context) {
	defer runtime.HandleCrash()
	defer c.workqueue.ShutDown()

	klog.Info("Starting DynamicRoute Controller")

	// 启动 informer
	go c.informerFactory.Start(ctx.Done())

	// 等待资源同步
	klog.Info("Waiting for informer caches to sync")
	if ok := cache.WaitForCacheSync(ctx.Done(), c.svcSynced); !ok {
		runtime.HandleError(fmt.Errorf("failed to wait for caches to sync"))
		return
	}
	klog.Info("Informer caches synced")

	go wait.UntilWithContext(ctx, c.runWorker, time.Second)

	<-ctx.Done()
	klog.Info("Shutting down DynamicRoute Controller")
}

// Stop 停止控制器
func (c *DynamicRouteController) Stop() {
	c.lock.Lock()
	defer c.lock.Unlock()

	close(c.stopCh)
	c.workqueue.ShutDown()
}

// runWorker 不断从队列中取出任务并进行处理
func (c *DynamicRouteController) runWorker(ctx context.Context) {
	for c.processNextWorkItem(ctx) {
	}
}

// processNextWorkItem 处理队列中的下一个工作项
func (c *DynamicRouteController) processNextWorkItem(ctx context.Context) bool {
	obj, shutdown := c.workqueue.Get()
	if shutdown {
		return false
	}
	defer c.workqueue.Done(obj)

	item, ok := obj.(WorkQueueItem)
	if !ok {
		c.workqueue.Forget(obj)
		runtime.HandleError(fmt.Errorf("expected WorkQueueItem in workqueue but got %#v", obj))
		return true
	}

	var err error
	switch item.Operation {
	case OperationAdd:
		err = c.serviceHandler.HandleAddService(ctx, item.Key)
	case OperationDelete:
		err = c.serviceHandler.HandleDeleteService(ctx, item.Key)
	default:
		runtime.HandleError(fmt.Errorf("unknown operation type: %s", item.Operation))
		c.workqueue.Forget(obj)
		return true
	}

	if err == nil {
		c.workqueue.Forget(obj)
		return true
	}

	runtime.HandleError(fmt.Errorf("error processing service %q (operation: %s): %v", item.Key, item.Operation, err))
	c.workqueue.AddRateLimited(obj)
	return true
}
