package dynamicroute

import (
	"fmt"
	corev1types "k8s.io/api/core/v1"
)

// getOnlineDevelopmentType 获取并验证在线开发类型，如果没有该注解则返回空字符串
func getOnlineDevelopmentType(svc *corev1types.Service) (string, error) {
	annotations := svc.GetAnnotations()
	if annotations == nil {
		return "", nil // 没有注解不是错误，可能是 RayCluster 服务
	}

	devType, exists := annotations[OnlineDevelopmentTypeAnnotationKey]
	if !exists {
		return "", nil // 没有 online-dev-type 注解，可能是 RayCluster 服务
	}

	// 验证类型是否有效
	switch devType {
	case CodeServer, Jupyter:
		return devType, nil
	default:
		return "", fmt.Errorf("服务 %s/%s 的 %s 注解值 '%s' 无效，支持的值: %s, %s",
			svc.Namespace, svc.Name, OnlineDevelopmentTypeAnnotationKey, devType, CodeServer, Jupyter)
	}
}

// isOnlineDevService 判断是否为在线开发服务
func isOnlineDevService(svc *corev1types.Service) bool {
	annotations := svc.GetAnnotations()
	if annotations == nil {
		return false
	}
	_, exists := annotations[OnlineDevelopmentTypeAnnotationKey]
	return exists
}

// isRayClusterService 判断是否为 RayCluster 服务
func isRayClusterService(svc *corev1types.Service) bool {
	return !isOnlineDevService(svc) // 有 managed-by 但没有 online-dev-type 的就是 RayCluster 服务
}

// shouldIncludeRewrite 判断是否需要包含 rewrite 规则
func shouldIncludeRewrite(devType string) bool {
	return devType == CodeServer
}

// extractServiceInfo 从 Kubernetes Service 对象中提取服务信息
func extractServiceInfo(svc *corev1types.Service) (*ServiceInfo, error) {
	devType, err := getOnlineDevelopmentType(svc)
	if err != nil {
		return nil, err
	}

	// 获取 ray cluster name（只有 RayCluster 服务需要）
	rayClusterName := ""
	if isRayClusterService(svc) {
		if svc.Labels != nil {
			rayClusterName = svc.Labels[RayClusterLabelKey]
		}
		if rayClusterName == "" {
			return nil, fmt.Errorf("RayCluster 服务 %s/%s 缺少 %s 标签", svc.Namespace, svc.Name, RayClusterLabelKey)
		}
	}

	ports := make([]ServicePort, 0, len(svc.Spec.Ports))
	for _, port := range svc.Spec.Ports {
		ports = append(ports, ServicePort{
			Name: port.Name,
			Port: port.Port,
		})
	}

	return &ServiceInfo{
		Name:           svc.Name,
		Namespace:      svc.Namespace,
		DevType:        devType,
		RayClusterName: rayClusterName,
		Ports:          ports,
	}, nil
}

// formatPortInfo 格式化端口信息用于日志输出
func formatPortInfo(ports []ServicePort) string {
	if len(ports) == 0 {
		return "无端口"
	}

	portInfo := ""
	for i, port := range ports {
		if i > 0 {
			portInfo += ", "
		}
		portInfo += fmt.Sprintf("%s(%d)", port.Name, port.Port)
	}
	return portInfo
}
